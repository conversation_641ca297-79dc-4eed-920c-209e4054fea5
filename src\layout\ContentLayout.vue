<template>
  <el-main style="overflow: hidden; padding: 0px" :style="{ height: +contentOptions.windowSize.height - headerHeight + 'px', 'background-color': 'var(--theme-color-default-background)' }">
    <!-- border-card / card -->
    <template v-if="contentOptions.isTab">
      <!-- 将 el-tabs 和组件内容分离，避免 el-tab-pane 生成多个页面实例 -->
      <el-tabs v-model="contentOptions.currentTab" type="border-card" closable class="goat-tabs" @tab-click="contentOptions.clickTab" @tab-remove="contentOptions.removeTab">
        <!-- 只渲染空的 tab-pane，不包含具体内容 -->
        <el-tab-pane v-for="item in contentOptions.items" :key="item.name" :label="item.title" :name="item.name">
        </el-tab-pane>
      </el-tabs>

      <!-- 在 tabs 外面渲染当前激活的组件，类似 router-view 的方式 -->
      <div class="tab-content-container" :style="{ height: +contentOptions.windowSize.height - headerHeight - 40 + 'px' }">
        <el-main class="content-main" :style="{ height: +contentOptions.windowSize.contentHeight + 2 + 'px' }">
          <el-scrollbar>
            <el-row justify="space-between" style="background-color: var(--theme-content-layout-bg-color)">
              <el-col :span="24">
                <div style="min-width: 850px" v-loading="getCurrentTabItem?.loading" element-loading-background="var(--theme-content-loading-bg-color)">
                  <!-- 暂时移除 keep-alive，先解决CPU 100%问题 -->
                  <component
                    :is="getCurrentTabItem?.componentName"
                    :key="`tab-component-${contentOptions.currentTab}`"
                    :tabItem="getCurrentTabItem"
                    :writeAble="getCurrentTabItem?.writeAble"
                    @tabRemove="tabRemove"
                    @openMenu="openMenu"
                    @selectMenuById="selectMenuById"
                    v-if="getCurrentTabItem && getCurrentTabItem.componentName">
                  </component>
                </div>
              </el-col>
            </el-row>
          </el-scrollbar>
        </el-main>
      </div>
    </template>
    <template v-else>
      <router-view />
    </template>
  </el-main>
</template>

<script >
/* contentOptions
{
  windowsSize : '',
  menus : [],
}
 
*/

import { ref, onMounted, inject, computed, onBeforeUnmount } from 'vue'
import Sortable from "sortablejs";

export default {
  components: {
  },
  name: 'ContentLayout',
  props: ['contentOptions'],
  setup(props) {
    const getHeader = inject('useConstGetHeader');
    const headerHeight = getHeader('height');

    // 添加组件销毁标志
    const isDestroying = ref(false)

    // 获取当前激活的tab项 - 使用computed确保响应式，添加错误处理
    const getCurrentTabItem = computed(() => {
      // 如果组件正在销毁，直接返回null
      if (isDestroying.value) {
        return null
      }

      try {
        if (!props.contentOptions?.currentTab || !props.contentOptions?.items || !Array.isArray(props.contentOptions.items)) {
          return null
        }
        const found = props.contentOptions.items.find(item => item && item.name === props.contentOptions.currentTab)
        return found || null
      } catch (error) {
        console.warn('Error in getCurrentTabItem:', error)
        return null
      }
    })

    // 组件销毁前设置标志
    onBeforeUnmount(() => {
      isDestroying.value = true
    })

    onMounted(() => {
      if (props.contentOptions.isTab) {
        let tabsNavEl = document.querySelector(".el-tabs__nav");
        Sortable.create(tabsNavEl, {
          //animation: 200,
          filter: ".el-icon-close",
          onEnd: props.contentOptions.moveEndTab
        });
      }
    })

    const tabRemove = (tabItem, isComponentName = false) => {
      props.contentOptions.removeTab(tabItem.name, isComponentName)
    }

    const openMenu = (menuName, data = null) => {
      props.contentOptions.openMenu(menuName, data)
    }

    const selectMenuById = (menuName, data = null) => {
      props.contentOptions.selectMenuById(menuName, data)
    }

    return {
      headerHeight,
      getCurrentTabItem,
      tabRemove,
      openMenu,
      selectMenuById,
    }
  }
}
</script>

<style scoped>
.el-tabs__header {
  margin: 0 0 0px;
  background-color: #fff;
}

.tab-content-container {
  overflow: hidden;
  background-color: var(--theme-color-default-background);
}
</style>
<style>
.content-main {
  padding: 0px;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 0px;
  /* 隐藏默认的tab内容区域，因为我们在外面渲染 */
  display: none;
}

/* 确保tabs头部样式正确 */
.goat-tabs .el-tabs__header {
  margin-bottom: 0;
}
</style>