<template>
  <el-main style="overflow: hidden; padding: 0px" :style="{ height: +contentOptions.windowSize.height - headerHeight + 'px', 'background-color': 'var(--theme-color-default-background)' }">
    <!-- border-card / card -->
    <template v-if="contentOptions.isTab">
      <el-tabs v-model="contentOptions.currentTab" type="border-card" closable class="goat-tabs" @tab-click="contentOptions.clickTab" @tab-remove="contentOptions.removeTab">
        <el-tab-pane v-for="item in contentOptions.items" :key="item.name" :label="item.title" :name="item.name" :style="{ height: +contentOptions.windowSize.height - headerHeight + 'px' }">
          <el-main class="content-main" :style="{ height: +contentOptions.windowSize.contentHeight + 2 + 'px' }">
            <el-scrollbar>
              <el-row justify="space-between" style="background-color: var(--theme-content-layout-bg-color)">
                <el-col :span="24">
                  <!-- <el-card class="content-card" style="minWidth:850px;" v-loading="item.loading"> -->
                  <div style="minwidth: 850px" v-loading="item.loading" element-loading-background="var(--theme-content-loading-bg-color)">
                    <component :is="item.componentName" :key="item.name" :tabItem="item" :writeAble="item.writeAble" @tabRemove="tabRemove" @openMenu="openMenu" @selectMenuById="selectMenuById"></component>
                  </div>
                  <!-- </el-card> -->
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-main>
        </el-tab-pane>
      </el-tabs>
    </template>
    <template v-else>
      <router-view />
    </template>
  </el-main>
</template>

<script >
/* contentOptions
{
  windowsSize : '',
  menus : [],
}
 
*/

import { ref, reactive, computed, onMounted, inject } from 'vue'
import Sortable from "sortablejs";

export default {
  components: {
  },
  name: 'ContentLayout',
  props: ['contentOptions'],
  setup(props, context) {
    const getHeader = inject('useConstGetHeader');
    const headerHeight = getHeader('height');

    onMounted(() => {
      if (props.contentOptions.isTab) {
        let tabsNavEl = document.querySelector(".el-tabs__nav");
        let sortTabs = Sortable.create(tabsNavEl, {
          //animation: 200,
          filter: ".el-icon-close",
          onEnd: props.contentOptions.moveEndTab
        });
      }
    })

    const tabRemove = (tabItem, isComponentName = false) => {
      props.contentOptions.removeTab(tabItem.name, isComponentName)
    }
    const openMenu = (menuName, data = null) => {
      props.contentOptions.openMenu(menuName, data)
    }

    const selectMenuById = (menuName, data = null) => {
      props.contentOptions.selectMenuById(menuName, data)
    }
    return {
      headerHeight,
      tabRemove,
      openMenu,
      selectMenuById,
    }
  }
}
</script>

<style scoped>
.el-tabs__header {
  margin: 0 0 0px;
  background-color: #fff;
}
</style>
<style>
.content-main {
  padding: 0px;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 0px;
}
</style>