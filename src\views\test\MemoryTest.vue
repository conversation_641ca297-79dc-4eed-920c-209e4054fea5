<template>
  <div class="memory-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>内存测试页面</span>
          <el-button type="primary" @click="createLargeData">创建大量数据</el-button>
          <el-button type="danger" @click="clearData">清理数据</el-button>
        </div>
      </template>
      
      <div class="memory-info">
        <p>当前数据量: {{ dataCount }}</p>
        <p>页面创建时间: {{ createTime }}</p>
        <p>最后更新时间: {{ lastUpdateTime }}</p>
      </div>
      
      <el-table :data="testData" style="width: 100%" max-height="400">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="timestamp" label="时间戳" width="180" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'

export default {
  name: 'MemoryTest',
  setup() {
    const testData = ref([])
    const dataCount = ref(0)
    const createTime = ref('')
    const lastUpdateTime = ref('')
    let timer = null
    let eventListener = null
    
    // 创建大量测试数据
    const createLargeData = () => {
      const newData = []
      for (let i = 0; i < 1000; i++) {
        newData.push({
          id: Date.now() + i,
          name: `测试项目 ${i}`,
          description: `这是一个测试描述，包含一些随机数据: ${Math.random().toString(36).substring(7)}`,
          timestamp: new Date().toLocaleString()
        })
      }
      testData.value = [...testData.value, ...newData]
      dataCount.value = testData.value.length
      lastUpdateTime.value = new Date().toLocaleString()
    }
    
    // 清理数据
    const clearData = () => {
      testData.value = []
      dataCount.value = 0
      lastUpdateTime.value = new Date().toLocaleString()
    }
    
    // 窗口大小变化监听器
    const handleResize = () => {
      console.log('窗口大小变化:', window.innerWidth, window.innerHeight)
    }
    
    onMounted(() => {
      createTime.value = new Date().toLocaleString()
      
      // 添加事件监听器
      eventListener = handleResize
      window.addEventListener('resize', eventListener)
      
      // 设置定时器
      timer = setInterval(() => {
        lastUpdateTime.value = new Date().toLocaleString()
      }, 5000)
      
      console.log('MemoryTest 组件已挂载')
    })
    
    onBeforeUnmount(() => {
      // 清理定时器
      if (timer) {
        clearInterval(timer)
        timer = null
        console.log('定时器已清理')
      }
      
      // 清理事件监听器
      if (eventListener) {
        window.removeEventListener('resize', eventListener)
        eventListener = null
        console.log('事件监听器已清理')
      }
      
      // 清理数据
      testData.value = []
      
      console.log('MemoryTest 组件已卸载，资源已清理')
    })
    
    return {
      testData,
      dataCount,
      createTime,
      lastUpdateTime,
      createLargeData,
      clearData
    }
  }
}
</script>

<style scoped>
.memory-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.memory-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.memory-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
