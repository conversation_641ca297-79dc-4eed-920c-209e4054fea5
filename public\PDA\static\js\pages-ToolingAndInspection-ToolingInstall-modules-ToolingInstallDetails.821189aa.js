(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-ToolingAndInspection-ToolingInstall-modules-ToolingInstallDetails"],{"066b":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uIcon:n("fe5a").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.show?n("v-uni-view",{staticClass:"u-empty",style:[t.emptyStyle]},[t.isSrc?n("v-uni-image",{style:{width:t.$u.addUnit(t.width),height:t.$u.addUnit(t.height)},attrs:{src:t.icon,mode:"widthFix"}}):n("u-icon",{attrs:{name:"message"===t.mode?"chat":"empty-"+t.mode,size:t.iconSize,color:t.iconColor,"margin-top":"14"}}),n("v-uni-text",{staticClass:"u-empty__text",style:[t.textStyle]},[t._v(t._s(t.text?t.text:t.icons[t.mode]))]),t.$slots.default||t.$slots.$default?n("v-uni-view",{staticClass:"u-empty__wrap"},[t._t("default")],2):t._e()],1):t._e()},r=[]},"07e6":function(t,e,n){"use strict";n.r(e);var i=n("68dd"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"086d":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var i={props:{icon:{type:String,default:uni.$u.props.empty.icon},text:{type:String,default:uni.$u.props.empty.text},textColor:{type:String,default:uni.$u.props.empty.textColor},textSize:{type:[String,Number],default:uni.$u.props.empty.textSize},iconColor:{type:String,default:uni.$u.props.empty.iconColor},iconSize:{type:[String,Number],default:uni.$u.props.empty.iconSize},mode:{type:String,default:uni.$u.props.empty.mode},width:{type:[String,Number],default:uni.$u.props.empty.width},height:{type:[String,Number],default:uni.$u.props.empty.height},show:{type:Boolean,default:uni.$u.props.empty.show},marginTop:{type:[String,Number],default:uni.$u.props.empty.marginTop}}};e.default=i},"0bec":function(t,e,n){"use strict";var i=n("f0e0"),a=n.n(i);a.a},1032:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2173")),r={name:"u-navbar",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{}},methods:{leftClick:function(){this.$emit("leftClick"),this.autoBack&&uni.navigateBack()},rightClick:function(){this.$emit("rightClick")}}};e.default=r},"1e2c":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-1658ae1f], uni-scroll-view[data-v-1658ae1f], uni-swiper-item[data-v-1658ae1f]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-navbar--fixed[data-v-1658ae1f]{position:fixed;left:0;right:0;top:0;z-index:11}.u-navbar__content[data-v-1658ae1f]{display:flex;flex-direction:row;align-items:center;height:44px;background-color:#9acafc;position:relative;justify-content:center}.u-navbar__content__left[data-v-1658ae1f], .u-navbar__content__right[data-v-1658ae1f]{padding:0 13px;position:absolute;top:0;bottom:0;display:flex;flex-direction:row;align-items:center}.u-navbar__content__left[data-v-1658ae1f]{left:0}.u-navbar__content__left--hover[data-v-1658ae1f]{opacity:.7}.u-navbar__content__left__text[data-v-1658ae1f]{font-size:15px;margin-left:3px}.u-navbar__content__title[data-v-1658ae1f]{text-align:center;font-size:16px;color:#303133}.u-navbar__content__right[data-v-1658ae1f]{right:0}.u-navbar__content__right__text[data-v-1658ae1f]{font-size:15px;margin-left:3px}',""]),t.exports=e},2173:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var i={props:{safeAreaInsetTop:{type:Boolean,default:uni.$u.props.navbar.safeAreaInsetTop},placeholder:{type:Boolean,default:uni.$u.props.navbar.placeholder},fixed:{type:Boolean,default:uni.$u.props.navbar.fixed},border:{type:Boolean,default:uni.$u.props.navbar.border},leftIcon:{type:String,default:uni.$u.props.navbar.leftIcon},leftText:{type:String,default:uni.$u.props.navbar.leftText},rightText:{type:String,default:uni.$u.props.navbar.rightText},rightIcon:{type:String,default:uni.$u.props.navbar.rightIcon},title:{type:[String,Number],default:uni.$u.props.navbar.title},bgColor:{type:String,default:uni.$u.props.navbar.bgColor},titleWidth:{type:[String,Number],default:uni.$u.props.navbar.titleWidth},height:{type:[String,Number],default:uni.$u.props.navbar.height},leftIconSize:{type:[String,Number],default:uni.$u.props.navbar.leftIconSize},leftIconColor:{type:String,default:uni.$u.props.navbar.leftIconColor},autoBack:{type:Boolean,default:uni.$u.props.navbar.autoBack},titleStyle:{type:[String,Object],default:uni.$u.props.navbar.titleStyle}}};e.default=i},"3b48":function(t,e,n){"use strict";n.r(e);var i=n("1032"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"451a":function(t,e,n){"use strict";n.r(e);var i=n("85df"),a=n("e6b2");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("8256");var o=n("828b"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"3f482ae2",null,!1,i["a"],void 0);e["default"]=u.exports},"4c77c":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("5479")),r=i(n("bde8")),o={name:"rollerConfirmDetail",mixins:[r.default],components:{NoData:a.default},data:function(){return{machineName:"",simpleTrackProduct:[]}},onLoad:function(t){this.machineName=t&&t.machineName,this.getData()},methods:{getData:function(){var t=this,e={machineName:this.machineName};this.$service.ToolingAndInspection.getDurableListByMachineName(e).then((function(e){e&&e.success&&e.datas.length>0&&(t.simpleTrackProduct=e.datas)}))}}};e.default=o},5479:function(t,e,n){"use strict";n.r(e);var i=n("e140"),a=n("6dfd");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);var o=n("828b"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"46eb7d74",null,!1,i["a"],void 0);e["default"]=u.exports},"63a4":function(t,e,n){var i=n("1e2c");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("31d08953",i,!0,{sourceMap:!1,shadowMode:!1})},"68dd":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2");var a=i(n("086d")),r={name:"u-empty",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{icons:{car:"购物车为空",page:"页面不存在",search:"没有搜索结果",address:"没有收货地址",wifi:"没有WiFi",order:"订单为空",coupon:"没有优惠券",favor:"暂无收藏",permission:"无权限",history:"无历史记录",news:"无新闻列表",message:"消息列表为空",list:"列表为空",data:"数据为空",comment:"暂无评论"}}},computed:{emptyStyle:function(){var t={};return t.marginTop=uni.$u.addUnit(this.marginTop),uni.$u.deepMerge(uni.$u.addStyle(this.customStyle),t)},textStyle:function(){var t={};return t.color=this.textColor,t.fontSize=uni.$u.addUnit(this.textSize),t},isSrc:function(){return this.icon.indexOf("/")>=0}}};e.default=r},"6dfd":function(t,e,n){"use strict";n.r(e);var i=n("db12"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"74a1":function(t,e,n){var i=n("c320");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("7c3ee4fd",i,!0,{sourceMap:!1,shadowMode:!1})},8256:function(t,e,n){"use strict";var i=n("74a1"),a=n.n(i);a.a},"85df":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("e8b2").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"bc_fff listPage"},[n("u-navbar",{attrs:{title:"工装安装-已安装明细",autoBack:!0,height:"50px",titleStyle:{color:"#fff"},leftIconColor:"#fff",leftIcon:"",leftText:"返回",placeholder:!0}}),n("v-uni-view",{staticClass:"listContainer ma10"},[n("v-uni-scroll-view",{staticClass:"h100x",attrs:{"scroll-y":!0,"scroll-top":t.scrollTop,"refresher-background":"#f3f3f7"},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.onScroll.apply(void 0,arguments)}}},[t.simpleTrackProduct.length>0?n("v-uni-view",t._l(t.simpleTrackProduct,(function(e,i){return n("v-uni-view",{key:i,staticClass:"mb10 br10 bc_fff pa10 b_dcdee2_dashed"},[n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("工装编码:")]),n("v-uni-view",[t._v(t._s(e.durableName))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("工装描述:")]),n("v-uni-view",[t._v(t._s(e.durableNameDesc))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("安装位置:")]),n("v-uni-view",[t._v(t._s(e.portName))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("额度使用寿命:")]),n("v-uni-view",[t._v(t._s(e.usedCountTotal))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("警戒使用寿命:")]),n("v-uni-view",[t._v(t._s(e.warningUsedCount))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("已使用寿命:")]),n("v-uni-view",[t._v(t._s(e.usedCount))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("剩余使用寿命:")]),n("v-uni-view",[t._v(t._s(e.residueUsedCount))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("安装时间:")]),n("v-uni-view",[t._v(t._s(e.eventTime))])],1),n("v-uni-view",{staticClass:"flex between h40 hcenter c_999"},[n("v-uni-view",[t._v("操作人员:")]),n("v-uni-view",[t._v(t._s(e.eventUser))])],1)],1)})),1):n("NoData")],1)],1)],1)},r=[]},"8c69":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uStatusBar:n("3846").default,uIcon:n("fe5a").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-navbar"},[t.fixed&&t.placeholder?n("v-uni-view",{staticClass:"u-navbar__placeholder",style:{height:t.$u.addUnit(t.$u.getPx(t.height)+t.$u.sys().statusBarHeight,"px")}}):t._e(),n("v-uni-view",{class:[t.fixed&&"u-navbar--fixed"]},[t.safeAreaInsetTop?n("u-status-bar",{attrs:{bgColor:t.bgColor}}):t._e(),n("v-uni-view",{staticClass:"u-navbar__content",class:[t.border&&"u-border-bottom"],style:{height:t.$u.addUnit(t.height),backgroundColor:t.bgColor}},[n("v-uni-view",{staticClass:"u-navbar__content__left",attrs:{"hover-class":"u-navbar__content__left--hover","hover-start-time":"150"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.leftClick.apply(void 0,arguments)}}},[t._t("left",[t.leftIcon?n("u-icon",{attrs:{name:t.leftIcon,size:t.leftIconSize,color:t.leftIconColor}}):t._e(),t.leftText?n("v-uni-text",{staticClass:"u-navbar__content__left__text",style:{color:t.leftIconColor}},[t._v(t._s(t.leftText))]):t._e()])],2),t._t("center",[n("v-uni-text",{staticClass:"u-line-1 u-navbar__content__title",style:[{width:t.$u.addUnit(t.titleWidth)},t.$u.addStyle(t.titleStyle)]},[t._v(t._s(t.title))])]),t.$slots.right||t.rightIcon||t.rightText?n("v-uni-view",{staticClass:"u-navbar__content__right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.rightClick.apply(void 0,arguments)}}},[t._t("right",[t.rightIcon?n("u-icon",{attrs:{name:t.rightIcon,size:"20"}}):t._e(),t.rightText?n("v-uni-text",{staticClass:"u-navbar__content__right__text"},[t._v(t._s(t.rightText))]):t._e()])],2):t._e()],2)],1)],1)},r=[]},9327:function(t,e,n){"use strict";var i=n("63a4"),a=n.n(i);a.a},a817:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-224c66ee], uni-scroll-view[data-v-224c66ee], uni-swiper-item[data-v-224c66ee]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-empty[data-v-224c66ee]{display:flex;flex-direction:row;flex-direction:column;justify-content:center;align-items:center}.u-empty__text[data-v-224c66ee]{display:flex;flex-direction:row;justify-content:center;align-items:center;margin-top:%?20?%}.u-slot-wrap[data-v-224c66ee]{display:flex;flex-direction:row;justify-content:center;align-items:center;margin-top:%?20?%}',""]),t.exports=e},bde8:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{model:[],scrollTop:0,refresherTriggered:!1,status:"loadmore",pageNumber:1,pageSize:10,searchModel:{},old:{scrollTop:0}}},methods:{refresherrefresh:function(){this.refresherTriggered||(this.refresherTriggered=!0,this.pageNumber=1,this.initSearchModel(),this.model=[],this.getData())},lower:function(){"nomore"!==this.status&&"loading"!==this.status&&(this.status="loading",this.pageNumber+=1,this.getData())},onScroll:function(t){this.old.scrollTop=t.detail.scrollTop},goTop:function(){this.scrollTop=this.old.scrollTop,this.$nextTick((function(){this.scrollTop=0,this.old.scrollTop=0}))}}}},c320:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */[data-v-3f482ae2] .u-navbar__content{background:#409eff!important}[data-v-3f482ae2] .u-toolbar__wrapper__confirm{color:#409eff!important}[data-v-3f482ae2] .u-tabs__wrapper__nav__line{background-color:#409eff!important}.icon-saoma[data-v-3f482ae2]{font-size:%?56?%;color:#000}',""]),t.exports=e},db12:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"NODATA",props:{mode:{type:String,default:"data"}}};e.default=i},e140:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uEmpty:n("e844").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"img-bg pt30"},[e("u-empty",{attrs:{mode:this.mode}})],1)},r=[]},e6b2:function(t,e,n){"use strict";n.r(e);var i=n("4c77c"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},e844:function(t,e,n){"use strict";n.r(e);var i=n("066b"),a=n("07e6");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("0bec");var o=n("828b"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"224c66ee",null,!1,i["a"],void 0);e["default"]=u.exports},e8b2:function(t,e,n){"use strict";n.r(e);var i=n("8c69"),a=n("3b48");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("9327");var o=n("828b"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"1658ae1f",null,!1,i["a"],void 0);e["default"]=u.exports},f0e0:function(t,e,n){var i=n("a817");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("dbd1fb64",i,!0,{sourceMap:!1,shadowMode:!1})}}]);