import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import Icons from 'unplugin-icons/vite'
import path from 'path'

import Components from 'unplugin-vue-components/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'


function rawTransform(fileRegex) {
  return {
    name: 'get-file-raw',
    transform(src, id) {
      if (fileRegex.filter((re) => re.test(id)).length > 0) {
        return `export default ${JSON.stringify(src)}`;
      }
    },
  };
}

// const isDev = import.meta.env.ENV === 'development';

export default defineConfig({
  // base: '/'+ (isDev ? '' : 'luoto-mes-web'),
  base: '/luoto-mes-web/',
  optimizeDeps: {
    exclude: ['crypto']
  },
  plugins: [
    vue(),
    vueJsx(),
    rawTransform([/\.bpmn$/]),
    Icons({
      compiler: 'vue3',  // 'vue2', 'vue3', 'jsx'
      autoInstall: true,
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@use "@/styles/_variables.scss" as *;'
      }
    }
  },
  server: {
    port: 8101,
    host: true,
    proxy: {
      '^/api': {
        target: 'http://**********:30844/api/luoto-mes', // 正式环境
        // target: 'http://localhost:8030/api/luoto-mes', // 后端本地
        // target: 'http://*************:32057/api/luoto-mes', // 测试本地
        changeOrigin: true, //是否跨域
        rewrite: (path) => path.replace('/api', '')
      },
      '^/board': {
        target: 'http://**********:30844/api/getech-lt-board', // 后端本地
        // target: 'http://localhost:8033/api/getech-lt-board', // 后端本地
        changeOrigin: true, //是否跨域
        rewrite: (path) => path.replace('/board', '')
      },
      '^/porosapi': {
        // target: 'https://uat.poros.getech.cn/api', // uat
        // target: 'http://*************:32057/api', // 测试环境
        target: 'http://**********:30844/api', // 替换 正式环境
        changeOrigin: true, //是否跨域
        rewrite: (path) => path.replace('/porosapi', '')
      }
    }
  },
  define: {
    'process.env': {},
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '/@utils': path.resolve(__dirname, './src/utils'),
      '/@modules': path.resolve(__dirname, './src/modules'),
      '/@components': path.resolve(__dirname, './src/components'),
      '/@stores': path.resolve(__dirname, './src/stores'),
      '@/flow': path.resolve(__dirname, './src/flow'),
      '/@flow': path.resolve(__dirname, './src/flow'),
    },
    extensions: ['.js']
  },
  build: {
    sourcemap: true,
  },
})


