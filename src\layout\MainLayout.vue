<template>
  <el-container :class="{ dark: isDark }" :style="{ 'max-height': windowSize.height + 'px' }">
    <el-header :height="headerHeight + 'px'" class="font-size-xl theme-main-in-header">
      <header-layout :propsHeaderOptions="headerOptions" v-model:isPopconMenu="isPopconMenu" v-model:currentActive="currentActive" v-model:currentTab="currentTab" v-model:isTab="isTab"></header-layout>
    </el-header>
    <el-container :style="{ 'max-height': windowSize.height - headerHeight + 'px' }">
      <side-layout v-model:isPopconMenu="isPopconMenu" :propsSideOptions="sideOptions">
        <template v-slot:[menuSlotName]>
          <el-scrollbar :max-height="menuSlotName == 'side-menu' ? windowSize.height - headerHeight : windowSize.height">
            <goat-menu ref="menuRef" :menuOptions="menuOptions"></goat-menu>
          </el-scrollbar>
        </template>
      </side-layout>
      <el-container>
        <content-layout :contentOptions="contentOptions"></content-layout>
      </el-container>
    </el-container>
  </el-container>
  <!-- <web-socket /> -->
</template>
<script>

import { ref, reactive, computed, onMounted, onUnmounted, inject, watch, nextTick } from 'vue'
import SideLayout from './SideLayout.vue'
import HeaderLayout from './HeaderLayout.vue'
import ContentLayout from './ContentLayout.vue'
import GoatMenu from '../components/navigation/GoatMenu.vue';
// import WebSocket from './socket/WebSocket.vue'
export default {
  components: {
    SideLayout,
    HeaderLayout,
    ContentLayout,
    GoatMenu,
    // WebSocket
  },
  setup() {
    const getHeader = inject("useConstGetHeader");
    const getAppName = inject("getAppName");
    const format = inject("format");
    const headerHeight = getHeader('height');
    const useAxiosPost = inject("useAxiosPost");
    const useAxiosGet = inject("useAxiosGet");
    const axiosPropsGet = inject("axiosPropsGet");
    const windowSize = inject('windowSize');
    const useMenuConvertMenu = inject('useMenuConvertMenu');
    const useMenuGetMenuByName = inject('useMenuGetMenuByName');
    const messageAlert = inject('messageAlert');
    const getPropertyOpen = inject("getPropertyOpen");
    const twoLayout = inject("getTwoLayout");
    const threeLayout = inject("getThreeLayout");
    const fourLayout = inject("getFourLayout");
    const isEmpty = inject("isEmpty");
    const deepCopy = inject("deepCopy");
    const loginUser = inject('loginUser');
    const emitter = inject('emitter')
    const setTableLayoutChange = inject('setTableLayoutChange')
    const useMenuGetMenuByContainsName = inject('useMenuGetMenuByContainsName');
    const initUserfavoriteMenu = inject('initUserfavoriteMenu');

    const user = loginUser.getUser();
    const messageConfirm = inject("messageConfirm");

    let isPropertyOpen = getPropertyOpen();

    // init menu slotname. side-menu | popcon-menu
    let menuSlotName = ref('side-menu');
    // init side menu. true | false
    let isSideMenu = ref(true);
    // init drawer. false | true
    let isPopconMenu = ref(false);
    // init side menu collapse. false | true
    let isCollapse = ref(false);
    // init tab content. true | false
    let isTab = ref(true);

    let menuRef = ref();


    let factoryName = ref('')
    const triggerTopMenu = () => {
      if (windowSize.width <= 1005) {
        showShortMenu();
      } else {
        showLongMenu();
      }
    }
    const showShortMenu = () => {
      isSideMenu.value = false;
      menuSlotName.value = 'popcon-menu'
      isCollapse.value = false;
    }

    const showLongMenu = () => {
      isSideMenu.value = true;
      menuSlotName.value = 'side-menu'
      isPopconMenu.value = false
    }
    window.addEventListener('resize', triggerTopMenu);

    triggerTopMenu();
    onUnmounted(() => {
      window.removeEventListener('resize', triggerTopMenu);
      document.removeEventListener('keydown', handleKeyDown);
    })
    /* end of init window size */

    // init side
    let sideOptions = reactive({
      width: 'auto',
      windowSize: windowSize
    });

    // init menu collapse 
    let radioOptions = reactive(
      {
        isCollapse: isCollapse,
        size: 'small',
        items: [
          { type: 'ICON', icon: { name: 'IconCaretLeft', size: '', color: '' }, text: 'collapse', value: true },
          { type: 'ICON', icon: { name: 'IconCaretRight', size: '', color: '' }, text: 'expand', value: false }
        ]
      }
    )

    let favoriteMenuButtonOptions = reactive({
      circle: true, onlyIcon: true, size: 'large',
      icon: {
        name: 'IconMenu',
      },
      isTooltip: true,
      tooltip: {
        text: 'Favorite Menu'
      },
      style: 'width:36px;height:33px',
      click: () => {
        //context.emit("update:isPopconMenu", true);
      }
    })



    // init header 
    let headerOptions = reactive({
      isTab: isTab,
      radioOptions: radioOptions,
      favoriteMenuButtonOptions: favoriteMenuButtonOptions,
      isSideMenu: isSideMenu,
      handleChangeShop: () => {

      },
      handlerActionHistory: () => {

      },
      handleTabAllClose: () => {
        currentTab.value = '';
        tabItems.value = [];
      },
      handleUserOpen: () => {

      },

    })

    /**
     * find menu
     */
    const findMenu = (items, menuId) => {
      let currentMenu;
      items.some((item) => {
        if (item.menuId == menuId) {
          currentMenu = item;
          return true
        }

        if (item.children) {
          currentMenu = findMenu(item.children, menuId);
          if (currentMenu !== undefined) {
            return true
          }
        }
        /*
        let findChild = item.items.find((childItem) =>{
          return childItem.index === index;
        });

        if(findChild !== undefined){
          currentMenu = findChild;
          return true;
        }
        */
      })
      return currentMenu;
    }

    let currentActive = ref();
    let menus = ref([]);
    let originalMenus = ref([]);

    const dealTreeData = (treeData) => {
      // treeData = treeData.filter(item => !item.hidden)
      const data = treeData.map((item) => ({
        ...item,
        appName: 'GFM',
        confirmFlag: 'N',
        iconName: item.englishName,
        labelText: item.name,
        menuId: item.id,
        menuName: item.mask,
        menuType: item.url,
        componentName: item.componentPath,
        parentMenuId: item.parentId,
        position: item.orderNum,
        tableName: item.mask,
        tableType: 'Def',
        trxId: item.id,
        // 如果children为空数组，则置为[]
        children: (item.children && item.children.length)
          ? dealTreeData(item.children)
          : [],
      }));
      return data;
    }

    const getMenu = (target) => {
      return new Promise(async (resolve, reject) => {
        // const onSuccess = (data) => {
        //   if (data.datas.length > 0) {
        //     target.value = useMenuConvertMenu(data.datas[0].children);
        //     originalMenus.value = useMenuConvertMenu(data.datas[0].children);
        //   }
        //   resolve()
        // }
        // const onSuccessProps = (data) => {
        //   if (data.data.menus.length > 0) {
        //     const menusList = dealTreeData(data.data.menus)
        //     target.value = useMenuConvertMenu(menusList);
        //     originalMenus.value = useMenuConvertMenu(menusList);
        //     localStorage.setItem("buttonMaks", JSON.stringify(data.data.buttonMaks))
        //   }
        //   resolve()
        // }
        // const onFailed = (data) => {
        //   messageAlert(data.message || data.msg, 'warning')
        //   resolve()
        // }
        // let data = {};
        // let appName = getAppName();
        // const flag = location.hostname == 'localhost'
        // if (false) useAxiosPost(format('/menu/%s', appName), data, null, onSuccess, onFailed)
        // else axiosPropsGet('/poros-permission/menu/menus', { sysCode: 'luoto-mes-web' }, onSuccessProps, onFailed, null)
        try {
          // 不适用原架构菜单
          //  AppMenus getAppMenus(target)
          let [porosMenusData,] = await Promise.all([getPorosMenus(target),])
          let systemId = porosMenusData.systemId
          let m1 = porosMenusData.menus
          // let m2 = AppMenus
          let menusList = []
          // const menusList = dealTreeData(m1)
          // target.value = useMenuConvertMenu(AppMenus);
          // originalMenus.value = useMenuConvertMenu(AppMenus);

          //  uat 环境才可以调用poros  网关鉴权 
          const isDev = import.meta.env.VITE_ENVIRONMENT === 'development';
          if (isDev) {
            menusList = dealTreeData(m1)
          } else {
            let findNlsPorosMenuRes = await findNlsPorosMenuTree(systemId)
            menusList = dealTreeData(findNlsPorosMenuRes)
          }
          target.value = useMenuConvertMenu(menusList);
          originalMenus.value = useMenuConvertMenu(menusList);
          loginUser.setPermissionbuttonMaks(porosMenusData.buttonMaks)
          resolve()
        } catch (error) {
          console.log('error', error);
        }
      })
    }



    const getPorosMenus = (target) => {
      return new Promise((res, rej) => {
        const onSuccess = (data) => {
          res(data.data)
        }
        const onFail = (data) => {
          messageAlert(data.message || data.msg, 'warning')
          rej(data)
        }
        axiosPropsGet('/poros-permission/menu/menus', { sysCode: 'luoto-mes-web' }, onSuccess, onFail, null)
      })
    }


    const findNlsPorosMenuTree = (systemld) => {
      return new Promise((res, rej) => {
        const onSuccess = (data) => {
          if (data.data.length > 0) {
            return res(data.data)
          }
        }
        const onFail = (data) => {
          messageAlert(data.message || data.msg, 'warning')
          return rej(data)
        }
        let params = {
          systemId: systemld,
        }
        useAxiosGet('/NlsDefApi/findNlsPorosMenuTree', params, onSuccess, onFail, null)
      })
    }

    const getAppMenus = (target) => {
      return new Promise((res, rej) => {
        const onSuccess = (data) => {
          res(data.datas[0].children)
        }
        const onFail = (data) => {
          messageAlert(data.message || data.msg, 'warning')
          rej(data)
        }
        let data = {};
        let appName = getAppName();
        useAxiosPost(format('/menu/%s', appName), data, null, onSuccess, onFail)
      })
    }


    const handleKeyDown = (event) => {
      if (event.key == 'F1') {
        event.preventDefault();
        isCollapse.value = !isCollapse.value
      }
    }


    let currentLocale = ref('')
    //  获取当前国际化语言
    const initCurrentLocal = () => {
      return new Promise((res, rej) => {
        const onSuccess = (data) => {
          if (data.datas.length > 0) {
            return res(data.datas[0])
          }
        }
        const onFail = (data) => {
          messageAlert(data.message || data.msg, 'warning')
          return rej(data)
        }
        let params = {}
        useAxiosGet('/LocaleController/queryCurrentLocal', params, onSuccess, onFail, null)
      })
    }

    const setUserInfo = () => {
      const onSuccess = (data) => {
        initCurrentLocal().then((currentLocale) => {
          loginUser.setUser({
            APPNAME: 'GFM',
            USERID: data.data.uid,
            TOKEN: user.TOKEN || '', // fix 开发环境页面刷新bug
            LOCALENAME: currentLocale,
            FACTORYNAME: factoryName.value, // 获取工厂
          }, []);
        })
        getMenu(menus)
          .then(() => {
            let appName = getAppName();
            if (appName == 'GOM') {
              let mainMenu = useMenuGetMenuByName(menus.value, 'Main');
              currentActive.value = mainMenu.menuId
              selectMenu(mainMenu.menuId)
            }
            nextTick(() => {
              const flag = location.hostname == 'localhost'
              let indexId = originalMenus.value[0].id
              menuRef.value && menuRef.value.menuOptions.select(indexId) // poro测试环境下首页菜单
              // menuRef.value && menuRef.value.menuOptions.select(flag ? '240' : '09aaa6c7850839c755033df1c5ed65e2') //240
            })
          })

        initUserfavoriteMenu()
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      axiosPropsGet('/poros-authcenter/user/message', {}, onSuccess, onFail, null)
    }


    const getEnumValue = () => {
      return new Promise((res, rej) => {
        const onSuccess = (data) => {
          if (data.datas.length > 0) {
            factoryName.value = data.datas[0].factoryName
            res(data)
          }
        }
        const onFail = (data) => {
          messageAlert(data.message || data.msg, 'warning')
          rej(data)
        }
        let body = {}
        let param = { queryId: 'getLastFactoryName' }
        useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
      })
    }
    onMounted(async () => {
      await getEnumValue()
      setUserInfo()
      document.addEventListener('keydown', handleKeyDown);
    })

    emitter.on("menuRefresh", () => {
      getMenu(menus)
    });
    emitter.on("menuFind", (text) => {
      if (isEmpty(text)) {
        menus.value = originalMenus.value
      } else {
        let copyMenus = deepCopy(originalMenus.value);
        menus.value = useMenuGetMenuByContainsName(copyMenus, text)
      }
    });

    let isDark = ref(false);
    emitter.on("isDarkTheme", (themeOn) => {
      isDark.value = themeOn
    });



    // const getNlsMap = (menuId) =>{
    //   return new Promise((resolve, reject) => {
    //     const onSuccess = (data) =>{
    //       let nlsMap = {}
    //       if(data.datas.length > 0){
    //         data.datas.forEach((item) =>{
    //           if(isEmpty(item.labelText) == false)
    //             nlsMap[item.labelKey] = item.labelText;
    //         })
    //       }
    //       resolve(nlsMap);
    //     }

    //     const onFailed = (data) => {
    //       messageAlert(data.message, 'warning')
    //       reject();
    //     }

    //     let appName = getAppName();
    //     let data = {
    //       appName : appName,
    //       menuId : menuId
    //     };

    //     useAxiosPost('/MenuItemDefApi/findAllWithEquals', data, null, onSuccess, onFailed)
    //   });
    // }



    const getNlsMap = (flag, searchMenu) => {
      return new Promise((resolve, reject) => {
        const onSuccess = (data) => {
          let nlsMap = {}
          if (data.datas.length > 0) {
            data.datas.forEach((item) => {
              if (isEmpty(item.labelText) == false)
                nlsMap[item.labelKey] = item.labelText;
            })
          }
          resolve(nlsMap);
        }

        const onFailed = (data) => {
          messageAlert(data.message || data.msg, 'warning')
          reject();
        }

        let appName = getAppName();
        let data = {}
        if (false) {
          data = {
            appName: appName,
            menuId: searchMenu.menuId
          }
        } else {
          data = {
            appName: appName,
            // description: searchMenu.componentName
            porosMenuId: searchMenu.id
          }
        }
        useAxiosPost('/MenuItemDefApi/findAllWithEquals', data, null, onSuccess, onFailed)
      });
    }

    const openConfirmUser = (next) => {
      emitter.emit('confirmUserOpen', next)
    }

    let isMenuProcessing = false;
    const selectMenu = (menuId, data = null, selectMenu = null) => {
      if (isMenuProcessing == false) {
        isMenuProcessing = true;
        let searchMenu = findMenu(menus.value, menuId);
        user.CURRENTMENU = searchMenu.labelText
        user.CURRENTCONFIRMFLAG = searchMenu.confirmFlag

        const tabs = tabItems.value
        let findTab = tabs.find((item) => {
          return item.name === menuId;
        });

        if (selectMenu == 'ProcessFlow') {
          // 工艺路线特殊处理
          removeTab(menuId)
        }
        // add tab
        if (findTab === undefined || selectMenu == 'ProcessFlow') {
          const flag = location.hostname == 'localhost'
          getNlsMap(flag, searchMenu)
            .then((nlsMap) => {
              let refData = ref({});
              if (data != null) {
                refData.value = data;
              }
              tabItems.value.push({
                title: searchMenu.labelText,
                name: searchMenu.menuId,
                menuItem: searchMenu,
                loading: false,
                writeAble: true, // searchMenu.accessFlagInherit == 'W' ? true : false
                processFlowEditAuth: data && data.processFlowEditAuth ? true : false,
                componentName: searchMenu.componentName,
                nlsMap: nlsMap,
                preData: refData,
                isPropertyOpen: isPropertyOpen,
                twoLayout: twoLayout(),
                threeLayout: threeLayout(),
                fourLayout: fourLayout(),
                openConfirmUser: openConfirmUser,
                setTableLayoutChange: setTableLayoutChange
              })

              currentActive.value = searchMenu.menuId;
              // update current tab value
              currentTab.value = searchMenu.menuId;
              isMenuProcessing = false;
            }).catch(() => {
              isMenuProcessing = false;
            })
        } else {
          if (data != null) {
            tabItems.value.forEach((tabItem) => {
              if (tabItem.name == menuId) {
                let refData = ref();
                refData.value = data;
                tabItem.preData = refData;
              }
            })
          }
          currentActive.value = menuId;
          // update current tab value
          currentTab.value = searchMenu.menuId;
          isMenuProcessing = false;
        }
      }
    }

    let menuOptions = reactive({
      mode: 'vertical',
      size: 'small',
      active: currentActive,
      windowSize: windowSize,
      isCollapse: isCollapse,
      isCollapseTransition: false,
      router: !isTab.value,
      menus: menus,
      select: (menuId, indexPath, item, routeResult) => {
        selectMenu(menuId)
      },
      open: (menuId, indexPath) => {
        console.log(menuId, indexPath);
        alert("open")
      }
    })

    const currentTab = ref('')
    const tabItems = ref([])

    const clickTab = (pane, ev) => {
      emitter.emit('commonLayoutChange')
    }

    const removeTab = (targetName, isComponentName = false) => {
      const tabs = tabItems.value
      let activeName = currentTab.value
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }
      console.log('targetName==', targetName, tabs);
      let name = ''
      let removedTab = null
      tabs.forEach(item => {
        if (item.name == targetName) {
          name = item.title
          removedTab = item
        }
      })

      // 内存清理函数
      const performTabRemoval = () => {
        // 清理被移除tab的数据引用
        if (removedTab) {
          // 清理preData引用
          if (removedTab.preData) {
            removedTab.preData.value = null
            removedTab.preData = null
          }
          // 清理其他可能的引用
          removedTab.nlsMap = null
          removedTab.menuItem = null
          removedTab.tabItem = null
        }

        currentTab.value = activeName
        if (isComponentName) {
          tabItems.value = tabs.filter((tab) => tab.componentName !== targetName)
        } else {
          tabItems.value = tabs.filter((tab) => tab.name !== targetName)
        }

        // 强制垃圾回收（在支持的浏览器中）
        nextTick(() => {
          if (window.gc && typeof window.gc === 'function') {
            window.gc()
          }
        })
      }

      if ((name == '其他工序' || name == '搅拌工序' || name == '烘烤工序' || name == '化成分容工序') && localStorage.getItem('cardCantEdit') === 'false') {
        messageConfirm('确认编辑界面的数据是否已保存', () => {
          performTabRemoval()
        }, 'warning')
        return
      }

      performTabRemoval()
    }

    const moveEndTab = (evt) => {
      console.log(evt)
    }

    const openMenu = (menuName, data = null) => {
      let mainMenu = useMenuGetMenuByName(originalMenus.value, menuName);
      currentActive.value = mainMenu.menuId
      selectMenu(mainMenu.menuId, data, menuName)
    }

    const contentOptions = reactive({
      windowSize: windowSize,
      isTab: isTab,
      currentTab: currentTab,
      items: tabItems,
      clickTab: clickTab,
      removeTab: removeTab,
      moveEndTab: moveEndTab,
      openMenu: openMenu,
      selectMenuById: selectMenu,  //for 收藏 Modeler 组件根据Id条件跳转
      maxCachedTabs: 10, // 最大缓存tab数量，防止内存过度占用
    })

    const setGlobalNls = () => {
      let globalNls = inject("globalNls");
      /* Global Nls Setting */
      const onSuccess = (data) => {
        let nlsMap = {}
        if (data.datas.length > 0) {
          data.datas.forEach((item) => {
            nlsMap[item.labelKey] = item.labelText;
          })
        }
        globalNls.setNls(nlsMap)
      }

      const onFailed = (data) => {
      }

      let appName = getAppName();
      let data = {
        appName: appName,
        menuId: "-1"
      };

      useAxiosPost('/MenuItemDefApi/findAllWithEquals', data, null, onSuccess, onFailed)
    }

    // 全局国际化
    setGlobalNls();

    return {
      menuRef,
      headerOptions,
      menuSlotName,
      headerHeight,
      radioOptions,
      sideOptions,
      menuOptions,
      windowSize,
      contentOptions,
      isPopconMenu,
      isSideMenu,
      currentTab,
      isTab,
      currentActive,
      isDark,
      user
    }
  }
}
</script>

<style scoped>
.el-header,
.el-footer {
  color: var(--el-text-color-primary);
}

.el-main {
  color: var(--el-text-color-primary);
  /* background-color: #e9eef3; */
  /* text-align: center; */
  /* line-height: 160px;*/
}

body > .el-container {
  margin-bottom: 40px;
}
</style>
